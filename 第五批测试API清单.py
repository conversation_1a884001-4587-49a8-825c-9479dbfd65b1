#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第五批测试API清单
专门用于第五批真正遗漏API的性能测试
包括：带参数的API、DELETE操作、特殊功能API、饮食建议API等
"""

# 第五批测试API列表 - 真正遗漏的34个API
FIFTH_BATCH_APIS = [
    # 会员系统API (4个)
    "检查支付状态",
    "创建自定义活动",
    "银行更新症状",
    "删除所有症状",

    # 论坛系统API (2个)
    "获取特定帖子",
    "帖子点赞",

    # 医案系统API (4个)
    "用户排名",
    "医案评分",
    "添加健康记录",
    "删除健康记录",

    # 症状管理API (1个)
    "删除症状",

    # 邀请系统API (1个)
    "扫描邀请码",

    # 预后搜索API (3个)
    "分类下的疗法",
    "疗法详情",
    "疗法点赞",

    # 医疗健康API (9个)
    "获取用户问卷列表",
    "获取最新计算结果",
    "计算问卷得分",
    "更新用户信息",
    "特定计算历史",
    "复杂问卷分析",
    "获取用户问卷视图",
    "体质历史记录",
    "健康经验排行榜",

    # 其他功能API (8个)
    "用户登出",
    "删除账户",
    "流式传输测试",
    "生成二维码",
    "总权重视图",
    "获取药物详情",
    "获取用户反馈",
    "举报功能",

    # 饮食建议API (2个)
    "个人AI饮食建议",
    "NLP饮食分析"
]

# 按业务模块分组
FIFTH_BATCH_BY_MODULE = {
    "会员系统": [
        "检查支付状态",
        "创建自定义活动",
        "银行更新症状",
        "删除所有症状"
    ],
    "论坛系统": [
        "获取特定帖子",
        "帖子点赞"
    ],
    "医案系统": [
        "用户排名",
        "医案评分",
        "添加健康记录",
        "删除健康记录"
    ],
    "症状管理": [
        "删除症状"
    ],
    "邀请系统": [
        "扫描邀请码"
    ],
    "预后系统": [
        "分类下的疗法",
        "疗法详情",
        "疗法点赞"
    ],
    "医疗健康": [
        "获取用户问卷列表",
        "获取最新计算结果",
        "计算问卷得分",
        "更新用户信息",
        "特定计算历史",
        "复杂问卷分析",
        "获取用户问卷视图",
        "体质历史记录",
        "健康经验排行榜"
    ],
    "其他功能": [
        "用户登出",
        "删除账户",
        "流式传输测试",
        "生成二维码",
        "总权重视图",
        "获取药物详情",
        "获取用户反馈",
        "举报功能"
    ],
    "饮食建议": [
        "个人AI饮食建议",
        "NLP饮食分析"
    ]
}

# 按风险等级分组
FIFTH_BATCH_BY_RISK = {
    "低风险": [
        "获取特定帖子",
        "用户排名",
        "分类下的疗法",
        "疗法详情",
        "获取用户问卷列表",
        "获取最新计算结果",
        "特定计算历史",
        "获取用户问卷视图",
        "体质历史记录",
        "健康经验排行榜",
        "生成二维码",
        "获取药物详情",
        "获取用户反馈"
    ],
    "中风险": [
        "检查支付状态",
        "创建自定义活动",
        "银行更新症状",
        "帖子点赞",
        "医案评分",
        "添加健康记录",
        "扫描邀请码",
        "疗法点赞",
        "计算问卷得分",
        "更新用户信息",
        "复杂问卷分析",
        "用户登出",
        "流式传输测试",
        "总权重视图",
        "举报功能"
    ],
    "高风险": [
        "删除所有症状",
        "删除健康记录",
        "删除症状",
        "删除账户",
        "个人AI饮食建议",
        "NLP饮食分析"
    ]
}

# 按API类型分组
FIFTH_BATCH_BY_TYPE = {
    "读取操作": [
        "获取特定帖子",
        "用户排名",
        "分类下的疗法",
        "疗法详情",
        "获取用户问卷列表",
        "获取最新计算结果",
        "特定计算历史",
        "获取用户问卷视图",
        "体质历史记录",
        "流式传输测试",
        "生成二维码",
        "获取药物详情",
        "获取用户反馈"
    ],
    "写入操作": [
        "检查支付状态",
        "创建自定义活动",
        "银行更新症状",
        "帖子点赞",
        "医案评分",
        "添加健康记录",
        "扫描邀请码",
        "疗法点赞",
        "计算问卷得分",
        "更新用户信息",
        "复杂问卷分析",
        "健康经验排行榜",
        "用户登出",
        "总权重视图",
        "举报功能",
        "个人AI饮食建议",
        "NLP饮食分析"
    ],
    "删除操作": [
        "删除所有症状",
        "删除健康记录",
        "删除症状",
        "删除账户"
    ]
}

# 按特殊属性分组
FIFTH_BATCH_BY_SPECIAL = {
    "带参数API": [
        "获取特定帖子",
        "帖子点赞",
        "用户排名",
        "医案评分",
        "删除健康记录",
        "扫描邀请码",
        "分类下的疗法",
        "疗法详情",
        "疗法点赞",
        "特定计算历史",
        "体质历史记录",
        "生成二维码",
        "获取药物详情"
    ],
    "DELETE操作": [
        "删除所有症状",
        "删除健康记录",
        "删除症状",
        "删除账户"
    ],
    "AI接口": [
        "个人AI饮食建议",
        "NLP饮食分析"
    ],
    "支付相关": [
        "检查支付状态"
    ]
}

def print_fifth_batch_summary():
    """打印第五批测试API汇总信息"""
    print("第五批测试API清单（真正遗漏的API）:")
    print("=" * 70)
    
    print(f"\n📊 总体统计:")
    print(f"   总API数量: {len(FIFTH_BATCH_APIS)} 个")
    print(f"   低风险API: {len(FIFTH_BATCH_BY_RISK['低风险'])} 个")
    print(f"   中风险API: {len(FIFTH_BATCH_BY_RISK['中风险'])} 个")
    print(f"   高风险API: {len(FIFTH_BATCH_BY_RISK['高风险'])} 个")
    print(f"   读取操作: {len(FIFTH_BATCH_BY_TYPE['读取操作'])} 个")
    print(f"   写入操作: {len(FIFTH_BATCH_BY_TYPE['写入操作'])} 个")
    print(f"   删除操作: {len(FIFTH_BATCH_BY_TYPE['删除操作'])} 个")
    
    print(f"\n🔍 特殊属性统计:")
    print(f"   带参数API: {len(FIFTH_BATCH_BY_SPECIAL['带参数API'])} 个")
    print(f"   DELETE操作: {len(FIFTH_BATCH_BY_SPECIAL['DELETE操作'])} 个")
    print(f"   AI接口: {len(FIFTH_BATCH_BY_SPECIAL['AI接口'])} 个")
    print(f"   支付相关: {len(FIFTH_BATCH_BY_SPECIAL['支付相关'])} 个")
    
    print(f"\n📋 按业务模块分组:")
    for module, apis in FIFTH_BATCH_BY_MODULE.items():
        print(f"\n🔹 {module} ({len(apis)}个API):")
        for i, api in enumerate(apis, 1):
            risk = "高风险" if api in FIFTH_BATCH_BY_RISK['高风险'] else \
                   "中风险" if api in FIFTH_BATCH_BY_RISK['中风险'] else "低风险"
            
            api_type = "删除" if api in FIFTH_BATCH_BY_TYPE['删除操作'] else \
                      "写入" if api in FIFTH_BATCH_BY_TYPE['写入操作'] else "读取"
            
            special = ""
            if api in FIFTH_BATCH_BY_SPECIAL['带参数API']:
                special += "[带参数]"
            if api in FIFTH_BATCH_BY_SPECIAL['AI接口']:
                special += "[AI接口]"
            if api in FIFTH_BATCH_BY_SPECIAL['支付相关']:
                special += "[支付]"
            
            print(f"   {i:2d}. {api:<25} [{risk}] [{api_type}] {special}")

    print(f"\n⚠️ 测试建议:")
    print(f"   • 低风险API: 可以正常并发测试")
    print(f"   • 中风险API: 建议低并发测试（≤15并发）")
    print(f"   • 高风险API: 严格限制并发（≤3并发）")
    print(f"   • DELETE操作: 极低并发，避免数据损坏")
    print(f"   • AI接口: 成本较高，限制并发和请求数")
    print(f"   • 带参数API: 注意参数有效性")

if __name__ == "__main__":
    print_fifth_batch_summary()
